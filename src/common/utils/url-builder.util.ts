import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

export class UrlBuilderUtil {
  /**
   * Builds the complete application URL based on environment and request context
   */
  static buildAppUrl(
    configService: ConfigService,
    port?: number,
    request?: Request,
  ): string {
    // Check if we have request headers that indicate the actual host
    if (request) {
      const forwardedProto = request.headers['x-forwarded-proto'] as string;
      const forwardedHost = request.headers['x-forwarded-host'] as string;
      const host = request.headers['host'] as string;

      if (forwardedProto && forwardedHost) {
        return `${forwardedProto}://${forwardedHost}`;
      }

      if (forwardedProto && host) {
        return `${forwardedProto}://${host}`;
      }

      if (host && !host.includes('localhost')) {
        return `https://${host}`;
      }
    }

    // Platform-specific detection
    if (process.env.RAILWAY_ENVIRONMENT_NAME) {
      // Railway deployment
      const railwayDomain = process.env.RAILWAY_PUBLIC_DOMAIN;
      if (railwayDomain) {
        return `https://${railwayDomain}`;
      }
    }

    if (process.env.VERCEL_URL) {
      return `https://${process.env.VERCEL_URL}`;
    }

    if (process.env.HEROKU_APP_NAME) {
      return `https://${process.env.HEROKU_APP_NAME}.herokuapp.com`;
    }

    // Force HTTPS in production
    if (process.env.FORCE_HTTPS === 'true' || process.env.NODE_ENV === 'production') {
      const host = process.env.HOST || 'localhost';
      return `https://${host}`;
    }

    // Default to localhost for development
    const defaultPort = port || parseInt(process.env.PORT || '4000', 10);
    return `http://localhost:${defaultPort}`;
  }

  /**
   * Builds the Google OAuth callback URL
   */
  static buildCallbackUrl(
    configService: ConfigService,
    callbackPath: string,
    request?: Request,
  ): string {
    const baseUrl = this.buildAppUrl(configService, undefined, request);
    return `${baseUrl}${callbackPath}`;
  }
} 