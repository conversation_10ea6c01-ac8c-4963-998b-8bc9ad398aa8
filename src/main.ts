import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { json, urlencoded } from 'express';
import { TenantContextService } from './modules/auth/tenant/tenant-context.service';

const session = require('express-session');
const passport = require('passport');

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);

  // Enable CORS
  app.enableCors({
    origin: process.env.CORS_ORIGIN || [
      'http://localhost:3000',
      'http://localhost:3001', 
      'https://docgic.com',
      'https://www.docgic.com',
      'https://api.docgic.com'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'Origin',
      'X-Requested-With',
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Headers',
      'Access-Control-Allow-Methods',
      'Access-Control-Allow-Credentials'
    ],
    exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
    preflightContinue: false,
    optionsSuccessStatus: 204
  });

  app.set('trust proxy', 1);
  app.setGlobalPrefix('api');

  // Middleware to prevent HTTPS redirect loops and handle preflight requests
  app.use((req, res, next) => {
    // Allow preflight requests to pass through
    if (req.method === 'OPTIONS') {
      return next();
    }

    // Prevent redirect loops by checking forwarded headers
    const forwardedProto = req.headers['x-forwarded-proto'];
    const host = req.headers['host'];
    
    // Don't redirect if we're already on HTTPS or if it's a localhost request
    if (forwardedProto === 'https' || host?.includes('localhost')) {
      return next();
    }

    // In production, redirect HTTP to HTTPS (but not for Railway health checks)
    if (process.env.NODE_ENV === 'production' && !req.path.includes('/health')) {
      return res.redirect(301, `https://${host}${req.url}`);
    }

    next();
  });

  // Session configuration
  app.use(
    session({
      secret: configService.get<string>('auth.sessionSecret'),
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: configService.get<string>('NODE_ENV') === 'production',
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      },
    }),
  );

  app.use(passport.initialize());
  app.use(passport.session());

  // Body parsing middleware
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      disableErrorMessages: false,
    }),
  );

  // Global filters
  app.useGlobalFilters(new AllExceptionsFilter(), new HttpExceptionFilter());

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Legal Document Analyzer API')
    .setDescription('API for analyzing legal documents')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = configService.get<number>('PORT') || 4000;
  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
}

bootstrap();
