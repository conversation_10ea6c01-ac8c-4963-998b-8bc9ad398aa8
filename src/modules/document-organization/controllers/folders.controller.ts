import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Logger,
  HttpStatus,
  HttpCode,
  Query,
  Inject,
  forwardRef,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { FoldersService } from '../services/folders.service';
import { 
  CreateFolderDto, 
  UpdateFolderDto, 
  FolderSharingDto, 
  FolderDocumentDto,
  FolderResponseDto 
} from '../dto/folder.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { DocumentsService } from '../../documents/services/documents.service';
import { Request } from 'express';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { UrlBuilderUtil } from '../../../common/utils/url-builder.util';

@ApiTags('document-organization-folders')
@Controller('document-organization/folders')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('document_organization')
@ApiBearerAuth()
export class FoldersController {
  private readonly logger = new Logger(FoldersController.name);

  constructor(
    private readonly foldersService: FoldersService,
    @Inject(forwardRef(() => DocumentsService))
    private readonly documentsService: DocumentsService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  private getApiUrl(request?: Request): string {
    return UrlBuilderUtil.buildAppUrl(this.configService, undefined, request);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new folder' })
  @ApiResponse({ status: 201, description: 'The folder has been successfully created.', type: FolderResponseDto })
  async create(
    @Body() createFolderDto: CreateFolderDto,
    @Organization() organizationId: string,
    @User() user: string | { userId: string },
  ) {
    this.logger.log(`Creating folder: ${createFolderDto.name}`);
    // Extract userId if user is an object
    const userId = typeof user === 'string' ? user : user.userId;
    const folder = await this.foldersService.create(createFolderDto, organizationId, userId);
    return this.mapToResponseDto(folder);
  }

  @Get()
  @ApiOperation({ summary: 'Get all folders' })
  @ApiResponse({ status: 200, description: 'Return all folders.', type: [FolderResponseDto] })
  @ApiQuery({ name: 'parentId', required: false, description: 'Filter by parent folder ID' })
  async findAll(
    @Organization() organizationId: string,
    @Query('parentId') parentId?: string,
  ) {
    this.logger.log('Getting folders');
    
    let folders;
    if (parentId === 'root') {
      // Get root folders (no parent)
      folders = await this.foldersService.findRootFolders(organizationId);
    } else if (parentId) {
      // Get child folders of a specific parent
      folders = await this.foldersService.findChildFolders(parentId, organizationId);
    } else {
      // Get all folders
      folders = await this.foldersService.findAll(organizationId);
    }
    
    return folders.map(folder => this.mapToResponseDto(folder));
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a folder by ID' })
  @ApiResponse({ status: 200, description: 'Return the folder.', type: FolderResponseDto })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  async findOne(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting folder with ID: ${id}`);
    const folder = await this.foldersService.findOne(id, organizationId);
    return this.mapToResponseDto(folder);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a folder' })
  @ApiResponse({ status: 200, description: 'The folder has been successfully updated.', type: FolderResponseDto })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  async update(
    @Param('id') id: string,
    @Body() updateFolderDto: UpdateFolderDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Updating folder with ID: ${id}`);
    const folder = await this.foldersService.update(id, updateFolderDto, organizationId);
    return this.mapToResponseDto(folder);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a folder' })
  @ApiResponse({ status: 204, description: 'The folder has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Deleting folder with ID: ${id}`);
    await this.foldersService.remove(id, organizationId);
  }

  @Patch(':id/sharing')
  @ApiOperation({ summary: 'Update folder sharing settings' })
  @ApiResponse({ status: 200, description: 'The folder sharing settings have been successfully updated.', type: FolderResponseDto })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  async updateSharing(
    @Param('id') id: string,
    @Body() folderSharingDto: FolderSharingDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Updating sharing settings for folder: ${id}`);
    const folder = await this.foldersService.updateSharing(id, folderSharingDto, organizationId);
    return this.mapToResponseDto(folder);
  }

  @Post(':id/documents')
  @ApiOperation({ summary: 'Add documents to a folder' })
  @ApiResponse({ status: 200, description: 'The documents have been successfully added to the folder.', type: FolderResponseDto })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  async addDocuments(
    @Param('id') id: string,
    @Body() folderDocumentDto: FolderDocumentDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Adding documents to folder: ${id}`);
    const folder = await this.foldersService.addDocuments(id, folderDocumentDto.documentIds, organizationId);
    return this.mapToResponseDto(folder);
  }

  @Delete(':id/documents')
  @ApiOperation({ summary: 'Remove documents from a folder' })
  @ApiResponse({ status: 200, description: 'The documents have been successfully removed from the folder.', type: FolderResponseDto })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  async removeDocuments(
    @Param('id') id: string,
    @Body() folderDocumentDto: FolderDocumentDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Removing documents from folder: ${id}`);
    const folder = await this.foldersService.removeDocuments(id, folderDocumentDto.documentIds, organizationId);
    return this.mapToResponseDto(folder);
  }

  @Get(':id/documents')
  @ApiOperation({ summary: 'Get all documents in a folder with details' })
  @ApiResponse({ status: 200, description: 'Return all documents in the folder with details.', type: 'array' })
  @ApiResponse({ status: 404, description: 'Folder not found.' })
  async getDocumentsInFolder(
    @Param('id') id: string,
    @Organization() organizationId: string,
    @Req() request: Request,
  ) {
    this.logger.log(`Getting documents in folder with ID: ${id}`);
    const folder = await this.foldersService.findOne(id, organizationId);
    
    // If no documents in the folder, return an empty array
    if (!folder.documentIds || folder.documentIds.length === 0) {
      return [];
    }
    
    // Get document details for each document ID in the folder
    const documents = [];
    
    for (const docId of folder.documentIds) {
      try {
                  // Get document directly from the documents API
          try {
            const apiUrl = this.getApiUrl(request);
            const response = await firstValueFrom(
              this.httpService.get(`${apiUrl}/api/documents/${docId}`, {
                headers: {
                  Authorization: request.headers.authorization,
              },
            })
          );
          
          const document = response.data;
          
          if (!document) {
            continue;
          }
          
          // Determine document type from filename extension
          const filename = document.filename || 'Unknown';
          const fileExtension = typeof filename === 'string' ? filename.split('.').pop()?.toLowerCase() : 'unknown';
          let documentType = 'unknown';
          
          if (fileExtension) {
            if (['pdf'].includes(fileExtension)) {
              documentType = 'pdf';
            } else if (['doc', 'docx'].includes(fileExtension)) {
              documentType = 'word';
            } else if (['txt', 'text'].includes(fileExtension)) {
              documentType = 'text';
            } else if (['xls', 'xlsx', 'csv'].includes(fileExtension)) {
              documentType = 'spreadsheet';
            } else if (['ppt', 'pptx'].includes(fileExtension)) {
              documentType = 'presentation';
            } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExtension)) {
              documentType = 'image';
            }
          }
          
          documents.push({
            id: docId.toString(),
            folderId: id,
            folderName: folder.name,
            filename: filename,
            type: documentType,
            extension: fileExtension,
            size: document.size || 0,
            uploadDate: document.uploadDate || new Date(),
            title: document.metadata?.title || filename || 'Untitled Document',
          });
        } catch (error) {
          this.logger.warn(`Could not fetch document ${docId} from API: ${error.message}`);
        }
      } catch (error) {
        this.logger.error(`Error processing document ${docId}: ${error.message}`);
      }
    }
    
    return documents;
  }

  @Get('documents/:documentId')
  @ApiOperation({ summary: 'Get all folders containing a document' })
  @ApiResponse({ status: 200, description: 'Return all folders containing the document.', type: [FolderResponseDto] })
  async findFoldersByDocument(
    @Param('documentId') documentId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting folders for document: ${documentId}`);
    const folders = await this.foldersService.findFoldersByDocumentId(documentId, organizationId);
    return folders.map(folder => this.mapToResponseDto(folder));
  }

  /**
   * Map a Folder entity to a FolderResponseDto
   */
  private mapToResponseDto(folder: any): FolderResponseDto {
    return {
      id: folder._id.toString(),
      name: folder.name,
      description: folder.description,
      parentId: folder.parentId ? folder.parentId.toString() : null,
      organizationId: folder.organizationId,
      createdBy: folder.createdBy,
      path: folder.path,
      documentCount: folder.documentCount,
      sharing: folder.sharing,
      createdAt: folder.createdAt,
      updatedAt: folder.updatedAt,
    };
  }
}
