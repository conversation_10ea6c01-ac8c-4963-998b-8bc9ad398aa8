import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { TenantContextService } from '../services/tenant-context.service';

/**
 * Middleware to capture request context information like IP address and user agent
 * for security auditing and tenant context
 */
@Injectable()
export class RequestContextMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestContextMiddleware.name);

  constructor(private readonly tenantContextService: TenantContextService) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Always allow OPTIONS requests (preflight) to pass through
    if (req.method === 'OPTIONS') {
      return next();
    }

    const context = this.tenantContextService.getCurrentTenant();
    
    if (context) {
      // Capture IP address - handle various proxy scenarios
      const ipAddress = 
        req.headers['x-forwarded-for'] as string || 
        req.socket.remoteAddress || 
        'unknown';
      
      // Capture user agent
      const userAgent = req.headers['user-agent'] || 'unknown';
      
      // Update the tenant context with request information
      const updatedContext = {
        ...context,
        ipAddress: Array.isArray(ipAddress) ? ipAddress[0] : ipAddress.split(',')[0].trim(),
        userAgent
      };
      
      // Run with the updated context
      this.tenantContextService.runWithContext(updatedContext, async () => {
        next();
      }).catch(error => {
        this.logger.error(`Error in request context middleware: ${error.message}`, error.stack);
        next(error);
      });
    } else {
      // If no tenant context exists yet, just proceed
      next();
    }
  }
}
