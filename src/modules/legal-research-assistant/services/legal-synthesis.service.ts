import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { AIService } from '../../ai/services/ai.service';
import {
  SynthesisResult,
  SearchResults,
  AnalysisContext,
  KeyFinding
} from '../interfaces/legal-research-result.interface';
import { ResearchOptions } from '../interfaces/legal-research-result.interface';
import { SYNTHESIS_STYLES } from '../constants/practice-areas.constants';

@Injectable()
export class LegalSynthesisService {
  private readonly logger = new Logger(LegalSynthesisService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => AIService))
    private readonly aiService: AIService,
  ) {}

  async synthesizeResults(
    query: string,
    searchResults: SearchResults,
    options: ResearchOptions,
    sessionContext?: {
      previousQueries: string[];
      previousFindings: string[];
    }
  ): Promise<SynthesisResult> {
    const startTime = Date.now();

    this.logger.debug(`Starting AI synthesis for query: "${query}"`);

    try {
      // Prepare analysis context
      const context = this.prepareAnalysisContext(query, searchResults, options, sessionContext);
      
      // Generate specialized legal prompt based on synthesis style
      const prompt = this.generateLegalAnalysisPrompt(context);

      // Get AI analysis with legal-specific settings
      const aiResponse = await this.aiService.generateResponse(prompt, {
        temperature: 0.3, // Lower temperature for more consistent legal analysis
        maxTokens: this.getMaxTokensForStyle(options.synthesisStyle),
        systemMessage: this.getLegalAnalysisSystemPrompt(options.synthesisStyle),
      });

      // Parse and structure the AI response
      const parsedAnalysis = this.parseAIAnalysis(aiResponse);

      // Format citations properly
      const formattedCitations = this.formatLegalCitations(
        parsedAnalysis.citations,
        searchResults.sources
      );

      // Calculate confidence score based on source quality and analysis depth
      const confidenceScore = this.calculateConfidenceScore(
        searchResults,
        parsedAnalysis,
        options
      );

      const duration = Date.now() - startTime;

      this.logger.debug(`AI synthesis completed in ${duration}ms with confidence ${confidenceScore}`);

      return {
        legalAnalysis: parsedAnalysis.analysis,
        keyFindings: parsedAnalysis.keyFindings,
        citations: formattedCitations,
        confidenceScore,
        practiceImplications: parsedAnalysis.implications,
        jurisdictionalNotes: parsedAnalysis.jurisdictionalNotes,
        recentDevelopments: parsedAnalysis.recentDevelopments,
        metadata: {
          duration,
          sourcesAnalyzed: searchResults.totalSources,
          aiModel: 'openai', // Default model name
          promptVersion: '1.0'
        }
      };

    } catch (error) {
      this.logger.error(`AI synthesis failed: ${error.message}`, error.stack);
      
      // Return a basic synthesis on failure
      return this.createFallbackSynthesis(query, searchResults, Date.now() - startTime);
    }
  }

  async generateTitleFromQuery(query: string): Promise<string> {
    const prompt = `Generate a concise, descriptive title (5-10 words) for a legal analysis session based on the following query. The title should capture the main legal topic or question.

IMPORTANT: Do NOT include the word "Research" in the title. Focus on the legal topic itself.

Query: "${query}"

Title:`;

    try {
      const title = await this.aiService.generateResponse(prompt, {
        temperature: 0.4,
        maxTokens: 30,
      });
      // Clean up the response, removing any quotes or extra text, and remove "Research" if it appears
      let cleanTitle = title.replace(/"/g, '').trim();
      
      // Remove "Research" and related terms from the beginning of the title
      cleanTitle = cleanTitle.replace(/^(Research|Legal Research|Case Research):\s*/i, '');
      cleanTitle = cleanTitle.replace(/^(Research|Legal Research|Case Research)\s+/i, '');
      
      return cleanTitle;
    } catch (error) {
      this.logger.error(`Failed to generate title for query: "${query}"`, error);
      // Fallback to a truncated query title
      return query.length > 80 ? query.substring(0, 77) + '...' : query;
    }
  }

  private prepareAnalysisContext(
    query: string,
    searchResults: SearchResults,
    options: ResearchOptions,
    sessionContext?: {
      previousQueries: string[];
      previousFindings: string[];
    }
  ): AnalysisContext {
    return {
      query,
      sources: searchResults.sources,
      options,
      sessionContext
    };
  }

  private generateLegalAnalysisPrompt(context: AnalysisContext): string {
    const { query, sources, options, sessionContext } = context;

    let prompt = `You are a legal research expert analyzing multiple sources to answer a legal research question.

RESEARCH QUESTION: ${query}

SOURCES TO ANALYZE:
${sources.map((source, index) => `
${index + 1}. ${source.title} ${source.citation ? `(${source.citation})` : ''}
   Authority: ${source.court || source.authority || 'Unknown'}
   Date: ${source.date}
   Jurisdiction: ${source.jurisdiction}
   Practice Area: ${source.practiceArea || 'General'}
   Snippet: ${source.snippet}
   URL: ${source.url}
   Authority Score: ${source.authorityScore.toFixed(2)}
   Relevance Score: ${source.relevanceScore.toFixed(2)}
`).join('\n')}`;

    // Add session context if available
    if (sessionContext?.previousQueries?.length) {
      prompt += `\n\nPREVIOUS RESEARCH CONTEXT:
Previous Questions: ${sessionContext.previousQueries.join('; ')}
Previous Key Findings: ${sessionContext.previousFindings?.join('; ') || 'None'}`;
    }

    // Add analysis requirements based on synthesis style
    prompt += this.getAnalysisRequirements(options.synthesisStyle);

    // Add response format
    prompt += `\n\nRESPONSE FORMAT (JSON):
{
  "analysis": {
    "text": "Comprehensive legal analysis including: (1) Overview of the legal issue and current state of law, (2) Detailed reasoning with precedent discussion and legal principles, (3) Conclusions and recommendations...",
    "sourceUrls": ["https://relevant_source_for_analysis1.com", "https://relevant_source_for_analysis2.com"]
  },
  "keyFindings": [
    {
      "finding": "Key finding 1 with specific legal principle or fact",
      "sourceUrls": ["https://source1.com", "https://source2.com"],
      "confidence": 0.95
    },
    {
      "finding": "Key finding 2 with supporting evidence",
      "sourceUrls": ["https://source3.com"],
      "confidence": 0.88
    }
  ],
  "citations": ["Properly formatted legal citation 1", "Citation 2", "Citation 3"],
  "implications": ["Practice implication 1", "Implication 2", "Implication 3"],
  "jurisdictionalNotes": "Any important jurisdictional considerations or conflicts...",
  "recentDevelopments": "Recent changes, trends, or emerging issues in this area of law..."
}

IMPORTANT GUIDELINES:
- Use proper legal citation format (Bluebook style)
- Distinguish between binding and persuasive authority
- Note any circuit splits or jurisdictional differences
- Highlight recent developments or changes in the law
- Provide practical implications for legal practitioners
- Assess the strength and reliability of the legal authority
- Be precise and accurate in legal terminology
- For the "analysis" field, populate "sourceUrls" with direct URLs that support the main points or citations within the "text".

KEY FINDINGS REQUIREMENTS:
- Each finding should be a specific, actionable legal principle or fact
- Include 1-3 source URLs that directly support each finding
- Confidence scores should reflect: 1.0 = statutory/regulatory authority, 0.9 = binding precedent, 0.8 = persuasive authority, 0.7 = secondary sources
- Prioritize findings that are most relevant to practitioners
- Match source URLs exactly from the provided sources list`;

    return prompt;
  }

  private getAnalysisRequirements(synthesisStyle?: string): string {
    switch (synthesisStyle) {
      case SYNTHESIS_STYLES.BRIEF:
        return `\n\nANALYSIS REQUIREMENTS (BRIEF STYLE):
1. Provide a concise legal analysis (2-3 paragraphs, under 300 words)
2. Identify 3-5 key legal findings
3. Focus on the most authoritative sources
4. Highlight immediate practical implications
5. Include brief overview, key points, and conclusions`;

      case SYNTHESIS_STYLES.ANALYTICAL:
        return `\n\nANALYSIS REQUIREMENTS (ANALYTICAL STYLE):
1. Provide deep legal analysis with reasoning
2. Examine precedent and legal principles in detail
3. Analyze conflicts between sources or jurisdictions
4. Discuss policy implications and legal theory
5. Compare different approaches or interpretations
6. Assess trends and future developments`;

      case SYNTHESIS_STYLES.COMPREHENSIVE:
      default:
        return `\n\nANALYSIS REQUIREMENTS (COMPREHENSIVE STYLE):
1. Provide a thorough overview of the legal landscape
2. Identify key legal findings and trends
3. Analyze the implications for legal practice
4. Note any jurisdictional differences or conflicts
5. Highlight recent developments or changes in the law
6. Assess the strength and reliability of legal authority
7. Provide practical guidance for practitioners`;
    }
  }

  private getLegalAnalysisSystemPrompt(synthesisStyle?: string): string {
    return `You are an expert legal researcher and analyst with deep knowledge of legal research methodology, citation practices, and legal writing. Your role is to synthesize legal research results into clear, accurate, and professionally formatted analysis.

Key responsibilities:
- Analyze legal sources with appropriate weight given to authority and jurisdiction
- Use proper legal citation format (Bluebook style)
- Distinguish between primary and secondary authority
- Identify binding vs. persuasive precedent
- Note circuit splits and jurisdictional variations
- Highlight recent developments and trends
- Provide practical implications for legal practitioners

Style: ${synthesisStyle || 'comprehensive'} - adjust depth and detail accordingly.

Always maintain accuracy, objectivity, and professional legal writing standards.`;
  }

  private getMaxTokensForStyle(synthesisStyle?: string): number {
    switch (synthesisStyle) {
      case SYNTHESIS_STYLES.BRIEF:
        return 800;
      case SYNTHESIS_STYLES.ANALYTICAL:
        return 2500;
      case SYNTHESIS_STYLES.COMPREHENSIVE:
      default:
        return 2000;
    }
  }

  private parseAIAnalysis(aiResponse: string): any {
    try {
      // Extract JSON from the AI response using improved logic
      const jsonText = this.extractJSONFromText(aiResponse);
      
      // Try to parse the extracted JSON
      let parsed;
      try {
        parsed = JSON.parse(jsonText);
      } catch (parseError) {
        this.logger.warn('Initial JSON parse failed, attempting to repair JSON', {
          error: parseError.message,
          position: parseError.message.match(/position (\d+)/)?.[1]
        });
        
        // Attempt to repair the JSON
        const repairedJson = this.attemptJSONRepair(jsonText);
        if (repairedJson) {
          parsed = JSON.parse(repairedJson);
          this.logger.debug('JSON repair successful');
        } else {
          throw parseError;
        }
      }
      
      // The AI response should have the expected structure
      return {
        keyFindings: this.processKeyFindings(parsed.keyFindings || []),
        analysis: {
          text: parsed.analysis?.text || '',
          sourceUrls: Array.isArray(parsed.analysis?.sourceUrls) ? parsed.analysis.sourceUrls : []
        },
        citations: Array.isArray(parsed.citations) ? parsed.citations : [],
        implications: Array.isArray(parsed.implications) ? parsed.implications : [],
        jurisdictionalNotes: parsed.jurisdictionalNotes || '',
        recentDevelopments: parsed.recentDevelopments || ''
      };

    } catch (error) {
      this.logger.error(`AI synthesis parsing failed: ${error.message}`, { 
        rawResponse: aiResponse.substring(0, 1000), // Show more of the response
        responseLength: aiResponse.length,
        stack: error.stack 
      });
      
      // Enhanced fallback parsing
      return this.createFallbackFromText(aiResponse);
    }
  }

  /**
   * Extract JSON from text with improved error handling
   */
  private extractJSONFromText(text: string): string {
    // First try the standard JSON code block pattern
    const jsonBlockRegex = /```json\s*([\s\S]*?)\s*```/s;
    const blockMatch = text.match(jsonBlockRegex);
    if (blockMatch && blockMatch[1]) {
      return blockMatch[1].trim();
    }

    // Try to find JSON object patterns
    const jsonObjectRegex = /({[\s\S]*})/s;
    const objectMatch = text.match(jsonObjectRegex);
    if (objectMatch && objectMatch[1]) {
      // Try to find the complete JSON object by counting braces
      const jsonCandidate = this.extractBalancedJSON(objectMatch[1]);
      if (jsonCandidate) {
        return jsonCandidate;
      }
    }

    // Try to find JSON array patterns
    const jsonArrayRegex = /(\[[\s\S]*\])/s;
    const arrayMatch = text.match(jsonArrayRegex);
    if (arrayMatch && arrayMatch[1]) {
      const jsonCandidate = this.extractBalancedJSON(arrayMatch[1]);
      if (jsonCandidate) {
        return jsonCandidate;
      }
    }

    this.logger.warn('Could not extract valid JSON from AI response');
    return text; // Return original text as fallback
  }

  /**
   * Extract balanced JSON by counting braces/brackets
   */
  private extractBalancedJSON(text: string): string | null {
    const trimmed = text.trim();
    if (!trimmed) return null;

    const firstChar = trimmed.charAt(0);
    if (firstChar !== '{' && firstChar !== '[') return null;

    const closeChar = firstChar === '{' ? '}' : ']';
    let balance = 0;
    let inString = false;
    let escaped = false;

    for (let i = 0; i < trimmed.length; i++) {
      const char = trimmed[i];

      if (escaped) {
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      if (char === '"' && !escaped) {
        inString = !inString;
        continue;
      }

      if (!inString) {
        if (char === firstChar) {
          balance++;
        } else if (char === closeChar) {
          balance--;
          if (balance === 0) {
            const candidate = trimmed.substring(0, i + 1);
            // Validate the extracted JSON
            try {
              JSON.parse(candidate);
              return candidate;
            } catch (e) {
              this.logger.debug('Balanced JSON extraction failed validation');
              continue;
            }
          }
        }
      }
    }

    return null;
  }

  /**
   * Attempt to repair malformed JSON
   */
  private attemptJSONRepair(jsonText: string): string | null {
    try {
      let repaired = jsonText.trim();

      // Remove any trailing commas before closing braces/brackets
      repaired = repaired.replace(/,(\s*[}\]])/g, '$1');

      // Ensure proper closing of incomplete strings
      const openQuotes = (repaired.match(/"/g) || []).length;
      if (openQuotes % 2 !== 0) {
        // Find the last quote and see if it needs closing
        const lastQuoteIndex = repaired.lastIndexOf('"');
        const afterQuote = repaired.substring(lastQuoteIndex + 1);
        if (!afterQuote.includes('"')) {
          repaired += '"';
        }
      }

      // Try to close incomplete objects/arrays
      let openBraces = 0;
      let openBrackets = 0;
      let inString = false;
      let escaped = false;

      for (let i = 0; i < repaired.length; i++) {
        const char = repaired[i];
        
        if (escaped) {
          escaped = false;
          continue;
        }

        if (char === '\\') {
          escaped = true;
          continue;
        }

        if (char === '"' && !escaped) {
          inString = !inString;
          continue;
        }

        if (!inString) {
          if (char === '{') openBraces++;
          else if (char === '}') openBraces--;
          else if (char === '[') openBrackets++;
          else if (char === ']') openBrackets--;
        }
      }

      // Close incomplete structures
      for (let i = 0; i < openBrackets; i++) {
        repaired += ']';
      }
      for (let i = 0; i < openBraces; i++) {
        repaired += '}';
      }

      // Validate the repaired JSON
      JSON.parse(repaired);
      return repaired;

    } catch (error) {
      this.logger.debug('JSON repair attempt failed', { error: error.message });
      return null;
    }
  }

  /**
   * Process key findings to ensure they have proper structure
   */
  private processKeyFindings(keyFindings: any[]): KeyFinding[] {
    if (!Array.isArray(keyFindings)) {
      return [];
    }

    return keyFindings
      .filter(finding => finding && typeof finding === 'object' && finding.finding)
      .map(finding => ({
        id: uuidv4(),
        finding: finding.finding.toString().trim(),
        sourceUrls: Array.isArray(finding.sourceUrls) ? finding.sourceUrls : [],
        confidence: typeof finding.confidence === 'number' ? finding.confidence : 0.7
      }))
      .slice(0, 10); // Limit to 10 findings max
  }

  private createFallbackFromText(text: string): any {
    this.logger.debug('Creating fallback analysis from AI response text');

    // Clean the text and split into lines
    const cleanText = text.replace(/```json|```/g, '').trim();
    const lines = cleanText.split('\n').filter(line => line.trim());
    
    // Try to extract analysis text from different patterns
    let analysisText = '';
    
    // Look for analysis patterns in the text
    const analysisPatterns = [
      /analysis['":]?\s*['":]?\s*([^"'}]+)/i,
      /legal analysis['":]?\s*['":]?\s*([^"'}]+)/i,
      /text['":]?\s*['":]?\s*([^"'}]+)/i,
      /The legal landscape[\s\S]*?(?=\n\n|\"|$)/i,
      /In\s+(?:summary|conclusion|analyzing)[\s\S]*?(?=\n\n|\"|$)/i
    ];

    for (const pattern of analysisPatterns) {
      const match = cleanText.match(pattern);
      if (match && match[1] && match[1].length > 100) {
        analysisText = match[1].trim().substring(0, 1500);
        break;
      }
    }

    // If no pattern match, extract substantial paragraphs
    if (!analysisText) {
      const substantialLines = lines.filter(line => 
        line.length > 100 && 
        !line.startsWith('http') && 
        !line.includes('keyFindings') &&
        !line.includes('citations') &&
        !line.includes('sourceUrls') &&
        !/^[\[\]{}",:\s]*$/.test(line) // Skip JSON syntax lines
      );
      analysisText = substantialLines.slice(0, 3).join(' ').substring(0, 1500);
    }

    // Extract key findings using various patterns
    const findings = this.extractFindingsFromText(cleanText, lines);

    // Extract citations
    const citations = this.extractCitationsFromText(cleanText);

    // Extract implications
    const implications = this.extractImplicationsFromText(cleanText, lines);

    // Extract URLs from the entire text
    const urlPattern = /https?:\/\/[^\s\)"\]]+/g;
    const urls = [...new Set(cleanText.match(urlPattern) || [])].slice(0, 10);

    this.logger.debug(`Fallback parsing extracted: ${findings.length} findings, ${citations.length} citations, ${implications.length} implications`);

    return {
      keyFindings: findings,
      analysis: { 
        text: analysisText || 'Analysis content could not be extracted from AI response. Please review the raw sources for detailed information.',
        sourceUrls: urls
      },
      citations,
      implications,
      jurisdictionalNotes: this.extractJurisdictionalNotes(cleanText),
      recentDevelopments: this.extractRecentDevelopments(cleanText)
    };
  }

  private extractFindingsFromText(text: string, lines: string[]): Array<{ id: string; finding: string; sourceUrls: string[]; confidence: number }> {
    const findings: Array<{ id: string; finding: string; sourceUrls: string[]; confidence: number }> = [];

    // Look for findings patterns
    const findingPatterns = [
      /finding['":]?\s*['":]?\s*([^"'}]+)/gi,
      /key finding['":]?\s*['":]?\s*([^"'}]+)/gi,
      /(The|A)\s+(?:court|law|case|ruling|decision|statute|regulation)[\s\S]{20,200}?(?=\.|;|\n)/gi,
      /(?:Importantly|Notably|Specifically|Additionally)[,:]?\s+([^.]{30,200})/gi
    ];

    for (const pattern of findingPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null && findings.length < 8) {
        const finding = match[1]?.trim();
        if (finding && finding.length > 20 && finding.length < 300) {
          findings.push({
            id: uuidv4(),
            finding: finding.replace(/['"{}]/g, '').trim(),
            sourceUrls: [],
            confidence: 0.6
          });
        }
      }
    }

    // If no pattern matches, extract meaningful sentences
    if (findings.length === 0) {
      const meaningfulLines = lines
        .filter(line => 
          line.length > 30 && 
          line.length < 300 &&
          !line.startsWith('http') &&
          !line.includes('sourceUrls') &&
          (line.includes('court') || line.includes('law') || line.includes('case') || 
           line.includes('ruling') || line.includes('decision') || line.includes('legal') ||
           line.includes('statute') || line.includes('regulation') || line.includes('precedent'))
        )
        .slice(0, 5);

      meaningfulLines.forEach(line => {
        findings.push({
          id: uuidv4(),
          finding: line.replace(/['"{}]/g, '').trim(),
          sourceUrls: [],
          confidence: 0.5
        });
      });
    }

    return findings.slice(0, 8); // Limit to 8 findings
  }

  private extractCitationsFromText(text: string): string[] {
    const citations: string[] = [];
    
    // Look for citation patterns
    const citationPatterns = [
      /citations?['":]?\s*\[([^\]]+)\]/gi,
      /\b\d+\s+[A-Z][a-z]+\s+\d+/g, // Basic case citation pattern
      /\b[A-Z][a-z]+\s+v\.?\s+[A-Z][a-z]+/g, // Case name pattern
      /\d+\s+U\.?S\.?\s+\d+/g, // US Supreme Court citations
      /\d+\s+F\.\d*d?\s+\d+/g // Federal reporter citations
    ];

    for (const pattern of citationPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleaned = match.replace(/citations?['":]?\s*\[/i, '').replace(/\]/g, '').trim();
          if (cleaned && cleaned.length > 5) {
            citations.push(cleaned);
          }
        });
      }
    }

    return [...new Set(citations)].slice(0, 8); // Remove duplicates and limit
  }

  private extractImplicationsFromText(text: string, lines: string[]): string[] {
    const implications: string[] = [];

    // Look for implication patterns
    const implicationPatterns = [
      /implications?['":]?\s*\[([^\]]+)\]/gi,
      /implications?['":]?\s*['":]?\s*([^"'}]+)/gi,
      /(?:This means|This suggests|This indicates|Therefore|Consequently|As a result)[\s:,]*([^.]{20,200})/gi,
      /(?:Practitioners|Lawyers|Attorneys)\s+should[\s\S]{10,150}?(?=\.|;|\n)/gi
    ];

    for (const pattern of implicationPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null && implications.length < 5) {
        const implication = match[1]?.trim();
        if (implication && implication.length > 15) {
          implications.push(implication.replace(/['"{}]/g, '').trim());
        }
      }
    }

    return [...new Set(implications)].slice(0, 5); // Remove duplicates and limit
  }

  private extractJurisdictionalNotes(text: string): string {
    const jurisdictionalPatterns = [
      /jurisdictional['":]?\s*['":]?\s*([^"'}]+)/gi,
      /(?:circuit split|jurisdictional difference|state variation)[\s\S]{10,200}?(?=\.|;|\n)/gi,
      /(?:federal|state)\s+(?:court|law|jurisdiction)[\s\S]{10,150}?(?=\.|;|\n)/gi
    ];

    for (const pattern of jurisdictionalPatterns) {
      const match = text.match(pattern);
      if (match && match[1] && match[1].length > 20) {
        return match[1].replace(/['"{}]/g, '').trim().substring(0, 500);
      }
    }

    return '';
  }

  private extractRecentDevelopments(text: string): string {
    const developmentPatterns = [
      /recent\s+developments?['":]?\s*['":]?\s*([^"'}]+)/gi,
      /(?:recent|new|emerging|evolving)[\s\S]{10,200}?(?=\.|;|\n)/gi,
      /(?:trend|development|change)[\s\S]{10,150}?(?=\.|;|\n)/gi
    ];

    for (const pattern of developmentPatterns) {
      const match = text.match(pattern);
      if (match && match[1] && match[1].length > 20) {
        return match[1].replace(/['"{}]/g, '').trim().substring(0, 500);
      }
    }

    return '';
  }

  private formatLegalCitations(citations: string[], sources: any[]): string[] {
    // Format citations according to legal standards
    return citations.map(citation => {
      // Find matching source for additional context
      const matchingSource = sources.find(source => 
        citation.includes(source.title) || 
        (source.citation && citation.includes(source.citation))
      );

      if (matchingSource?.citation) {
        return matchingSource.citation;
      }

      // Basic citation formatting
      return citation.trim();
    });
  }

  private calculateConfidenceScore(
    searchResults: SearchResults,
    parsedAnalysis: any,
    options: ResearchOptions
  ): number {
    let confidence = 0.5; // Base confidence

    // Factor in source quality
    const avgAuthorityScore = searchResults.sources.reduce(
      (sum, source) => sum + source.authorityScore, 0
    ) / searchResults.sources.length;
    confidence += avgAuthorityScore * 0.3;

    // Factor in number of sources
    const sourceCount = searchResults.sources.length;
    if (sourceCount >= 5) confidence += 0.1;
    if (sourceCount >= 10) confidence += 0.1;

    // Factor in analysis depth
    if (parsedAnalysis.keyFindings?.length >= 3) confidence += 0.1;
    if (parsedAnalysis.citations?.length >= 2) confidence += 0.1;

    // Factor in recency of sources
    const recentSources = searchResults.sources.filter(source => {
      const sourceDate = new Date(source.date);
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      return sourceDate > oneYearAgo;
    });
    
    if (recentSources.length > 0) {
      confidence += (recentSources.length / sourceCount) * 0.1;
    }

    return Math.min(1.0, Math.max(0.1, confidence));
  }

  private createFallbackSynthesis(
    query: string,
    searchResults: SearchResults,
    duration: number
  ): SynthesisResult {
    return {
      legalAnalysis: {
        text: `Research completed for: "${query}". Found ${searchResults.totalSources} relevant sources. AI synthesis service is currently unavailable. Please review the individual sources for detailed analysis.`,
        sourceUrls: []
      },
      keyFindings: [
        {
          id: uuidv4(),
          finding: `${searchResults.totalSources} sources identified`,
          sourceUrls: [],
          confidence: 0.8
        },
        {
          id: uuidv4(),
          finding: 'AI synthesis temporarily unavailable',
          sourceUrls: [],
          confidence: 1.0
        },
        {
          id: uuidv4(),
          finding: 'Manual review of sources recommended',
          sourceUrls: [],
          confidence: 0.9
        }
      ],
      citations: searchResults.sources
        .filter(source => source.citation)
        .map(source => source.citation!)
        .slice(0, 5),
      confidenceScore: 0.3,
      practiceImplications: ['Manual review of sources recommended'],
      metadata: {
        duration,
        sourcesAnalyzed: searchResults.totalSources,
        aiModel: 'fallback',
        promptVersion: '1.0'
      }
    };
  }
}
